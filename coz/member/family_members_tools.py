import requests
from logger import logger
import  os
from coz.conversation_store import member_conversation_histories as  member_histories

urlHost = os.environ["host"]
def query_family_member_name(device_sn: str, timezone: str):
    member_list = query_family_members(device_sn,timezone)
    member_name_list = [member.get('memberName', '') for member in member_list]
    return member_name_list

def query_family_members(device_sn: str, timezone: str):

    result = [
        {
            "memberUid": "1111111",
            "memberName": "Tom"
        },
        {
            "memberUid": "22222",
            "memberName": "jack"
        },
        {
            "memberUid": "33333",
            "memberName": "jerry"
        }
    ]
    member_histories[device_sn + "_members"] = result
    #result  list ,from the result list get member_name_list
    return result
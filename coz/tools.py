import inspect
from typing import Annotated as A, Literal


def get_name(
    id: A[int, "编号，必填值"],
    name: A[Literal["a", "b"], "姓名，可选值"] = None,
) -> str:
    """根据编号获取ID"""

# __desc__
# @tool(desc="根据编号获取ID")
def get_name2(
    id: A[int, "编号，必填值"],
    name: A[Literal["a", "b"], "姓名，可选值"] = None,
) -> str:
    """根据编号获取ID"""
    """根据编号获取ID...."""


tools = [get_name, get_name2]


def get_tool_propmt(tools):
    prompt = "以下是可用的工具列表：\n\n"
    for tool in tools:
        tool_propmt = f"工具名：{tool.__name__}\n工具描述：{tool.__doc__ }\n参数描述： {tool.__name__} { inspect.signature(tool)}\n\n"
        prompt += tool_propmt
    return prompt

print(get_tool_propmt(tools))

import datetime
import json
import re
from typing import List, Dict
from fastapi import HTTPException, Request
from fastapi import APIRouter
from langchain_core.messages import HumanMessage, BaseMessage, AIMessage, SystemMessage, ToolMessage
from pydantic import BaseModel
from starlette.responses import StreamingResponse
from xmltodict import parse
from logger import logger

# This will store simple lists of dictionaries, not complex objects
from coz.conversation_store import meals_conversation_histories as conversation_histories, current_meal, current_meal_dict
from coz.meals.meals_agent_service import MealNameType, cozMealsAgent, CozMealsGraphState, get_food_suggestion

coz_meals_api_router = APIRouter()

SYSTEM_PROMPT = open("coz/prompt.md", "r").read()


md = str
CONTEXT_PROMPT: md = """
## 上下文信息

当前日期、时间、会话 ID、时区、设备序列号等信息会动态插入，确保对话有明确的上下文。

The current date and time is: <current_time_str>.
The current  session_id is <session_id>
The current  timeZoneId is <timezone>
The current deviceSn is: <deviceSn>.

当前饮食日程信息如下：
- 当前用户指定的日程日期(mealDate): <currentMealDate>
- 当前用户指定的餐食类型(mealKind): <currentMealKind>
- 当前用户指定的菜品列表(mealNames):  <currentMealNames>

后续对当前用户指定的菜品列表的修改，都需要基于当前饮食日程信息进行修改

## 用户问题

"""


def prompt_format(prompt: str, json_output=False, **kwargs):
    prompt2 = prompt
    print("kwargs: ", kwargs)
    for k, v in kwargs.items():
        if json_output:
            prompt2 = prompt2.replace(f"<{k}>\n</{k}>", f"<{k}>\n{json.dumps(v,ensure_ascii=False,indent=2)}\n</{k}>")
        else:
            prompt2 = prompt2.replace(f"<{k}>", str(v))
    return prompt2


# 定义请求体模型
class ChatRequest(BaseModel):
    message: str = None
    selectedMealNames: List[str] = None


# --- Helper Functions for Manual Conversion ---
# def history_to_dicts(history: List[BaseMessage]) -> List[Dict]:
#     """Converts a list of message objects into a list of dictionaries."""
#     serializable_history = []
#     for message in history:
#         # Determine the type of the message
#         if isinstance(message, HumanMessage):
#             msg_type = "human"
#         elif isinstance(message, AIMessage):
#             msg_type = "ai"
#         else:
#             # You can add logic for other types like ToolMessage if needed
#             continue
#         # Create a simple dictionary
#         serializable_history.append({"type": msg_type, "content": message.content})
#     return serializable_history

# def dicts_to_history(dicts: List[Dict]) -> List[BaseMessage]:
#     """Converts a list of dictionaries back into a list of message objects."""
#     history = []
#     for item in dicts:
#         # Re-create the message object based on its type
#         if item.get("type") == "human":
#             history.append(HumanMessage(content=item.get("content", "")))
#         elif item.get("type") == "ai":
#             history.append(AIMessage(content=item.get("content", "")))
#     return history


def initSystemPrompt(history, deviceSn, graph_state: CozMealsGraphState, request: ChatRequest, timezone, session_id):
    """返回系统提示词和用户输入"""
    global current_meal
    # if not any(isinstance(msg, SystemMessage) for msg in history):

    # 1. 获取当前时间和日期
    # 为了准确性，我们使用一个具体的时区，例如美国太平洋时间 (PDT)
    # 你可以换成你需要的任何时区，如 'Asia/Shanghai'
    now = datetime.datetime.now()
    current_time_str = now.strftime("%Y-%m-%d %H:%M:%S %Z")

    # 2. 创建动态的System Prompt
    # 使用f-string将当前时间字符串插入到我们的模板中
    #     system_prompt_content = f"""
    #         You  smart chef assistant helping users to arrange the meal, user call you Cozy,
    #         The current date and time is: {current_time_str}.
    #         The current  session_id is {session_id}
    #         The current  timeZoneId is {timezone}
    #         The current deviceSn is: {deviceSn}.
    #         You are a smart chef assistant helping users to arrange meals, user calls you Cozy.
    #         The current date and time is: {current_time_str}.
    #         ...
    #         firstly, analyze the user's intent, user either want to add a meal or change a meal. for example, user can say 'i want to have apple pie tomorrow 8pm.', then the intent would be 'add apple pie'.
    #         another example, if user say 'i want to change cola to tea for tomorrow 8pm.', then the intent would be 'change cola to tea'.
    #         you should use the get_food_suggestion tool to get the food name to suggest to user. if user has not provided the food name, it means user is asking for a food suggestion, call get_food_suggestion with empty food name, it will return a list of user's favorite food.
    #         if user has provided the food name, call get_food_suggestion, it will return the most similar food name to the user's input. suggest them to user.
    #         remember to call get_food_suggestion if no suggestion_food information in info_history, and save the output of get_food_suggestion in suggestion_food,
    #         for example, if user say 'i want to have salad', get_food_suggestion may return '1.chicken salad, 2.salmon salad, 3. cola,etc.' then you should ask user 'would you like 1.chicken salad, or 2.salmon salad?'
    #         after user choose the food, you should update the intendMeal with the food name, and update the user_intent also, for example, if user choose 'chicken salad', then the intendMeal should be 'chicken salad', and the user_intent should be 'add chicken salad'.
    #         user can choose the food name by the number, for example, if user choose '1 and 3', then the intendMeal should be 'chicken salad, cola', and the user_intent should be 'add chicken salad and cola'.
    #         don't ask user to provide the information you have already got.
    #          mealDate are always required, intendMeal is string contains one or more food names, food name can be cola, noodles, beaf, etc. mealDate is always in the format of YYYY-MM-DD.
    #         selectedMealNames is not provided then no need to care about  intendMeal

    #         mealKind can be  breakfast, lunch, dinner, snack are all valid meal kinds. user can directly input the meal kind or the time of the meal. if user input the time of the meal, you should turn it into breakfast, lunch, dinner or snack. follow the rules below:
    #         Snack: can be any time of the day, user have to say it explicitly, for example, 'i want to have a snack at 10am'
    #         Breakfast: morning, 06:00 - 10:00
    #         Lunch: noon, 11:00 - 14:00
    #         Dinner: Evening, night, 18:00 - 24:00
    #         if user select a time out of breakfast, lunch, dinner and user does not say explitly is snack, you should ask user tell the meal kind explicitly.
    #         if user want to arrange more than one mealKind, e.g. breakfast and lunch, you should choose one of them as the mealKind, and tell user something like 'let's arrange it one by one, how about breakfast first?'
    #         when you think you have got all the required information, you should call the check_meal_infos tool to check if all the information is provided.
    #         for example, if lunch on 2025-06-04 is pizza, pie and coffee, and the user want to add a soup, then the meal of 2025-06-04 lunch should be pizza, pie, soup and coffee.
    #         another example, if lunch on 2025-06-04 is pizza, pie, soup and coffee, and the user want to change coffee to tea, then the meal of 2025-06-04 lunch should be pizza, pie, soup and tea.
    #         after you decide the meal information, set all_info_provided to True, and save it to summary.

    #         every time invoke one tool,if get_food_suggestion invoked ,should not invoke  collect_coz_meals_info
    #    """
    context_prompt = str(CONTEXT_PROMPT)
    context_prompt = prompt_format(
        context_prompt,
        current_time_str=current_time_str,
        session_id=session_id,
        timezone=timezone,
        deviceSn=deviceSn,
        currentMealDate=current_meal["mealDate"],
        currentMealKind=current_meal["mealKind"],
        currentMealNames=current_meal["mealNames"],
    )
    # current_meal = {"mealDate": graph_state["mealDate"], "mealKind": graph_state["mealKind"], "mealNames": graph_state["mealNames"]}
    # system_prompt_content = prompt_format(system_prompt_content, json_output=True, current_meal=current_meal)

    # 3. 准备给LLM的消息列表
    # history.append(SystemMessage(content=system_prompt_content))
    # graph_state["messages"].append(SystemMessage(content=system_prompt_content))
    # system_message = SystemMessage(content=system_prompt_content)

    # 用户输入加入 messages
    # 检查相同的用户信息是否已经在会话历史记录中,如果没有则添加
    selectedMealNames = request.selectedMealNames
    human_message = HumanMessage(content=context_prompt + request.message)
    if selectedMealNames is not None and selectedMealNames != "":
        conversation_histories[deviceSn + "_meals_selectedMealNames" + session_id] = selectedMealNames
        # msg = HumanMessage(content=json.dumps({"selectedMealNames": selectedMealNames}))
        # graph_state["messages"].append(msg)
        msg = "user selected meal names: " + ", ".join(selectedMealNames)
        graph_state["messages"].append(HumanMessage(content=msg))
        human_message = HumanMessage(content=context_prompt + msg)
    else:
        # if request.message is not None and request.message not in [msg.content for msg in history]:
        graph_state["messages"].append(HumanMessage(content=request.message))
        human_message = HumanMessage(content=context_prompt + request.message)

    # return list(graph_state["messages"])
    return [human_message]


# @coz_meals_api_router.post("/coz_meals")
# def stream(request: ChatRequest, headers_request: Request):
#     # 获取请求头的 head 信息
#     # 这里假设请求头中包含了 timeZoneId 和 deviceSn
#     timezone = headers_request.headers.get("timeZoneId", "")  # 默认时区为 Asia/Shanghai
#     deviceSn = headers_request.headers.get("deviceSn", "")  # 默认设备序列号
#     session_id = headers_request.headers.get("sessionid", "")
#     if timezone == "" or deviceSn == "" or session_id == "":
#         logger.error("Missing required headers: timeZoneId or deviceSn or sessionid")
#         raise HTTPException(status_code=400, detail="Missing required headers: timeZoneId or deviceSn")
#     logger.info(f"coz_meals_receive {deviceSn} request: {request}")

#     # 加载历史
#     raw_history_dicts = conversation_histories.get(deviceSn + "_meals_" + session_id, [])
#     history = dicts_to_history(raw_history_dicts)
#     # 构造 GraphState
#     graph_state: CozMealsGraphState = {"messages": list(history)}
#     selectedMealNames_history = conversation_histories.get(deviceSn + "_meals_selectedMealNames" + session_id, [])

#     full_messages = initSystemPrompt(history, deviceSn, graph_state, request, timezone, session_id)
#     if full_messages is None:
#         return "error"
#     response = cozMealsAgent.invoke({"messages": full_messages, "selectedMealNames": request.selectedMealNames})
#     return response


@coz_meals_api_router.post("/coz_meals_steam", response_class=StreamingResponse)
def stream2(request: ChatRequest, headers_request: Request):
    # 获取请求头的 head 信息
    # 这里假设请求头中包含了 timeZoneId 和 deviceSn
    timezone = headers_request.headers.get("timeZoneId", "")  # 默认时区为 Asia/Shanghai
    deviceSn = headers_request.headers.get("deviceSn", "")  # 默认设备序列号
    session_id = headers_request.headers.get("sessionid", "")
    if timezone == "" or deviceSn == "" or session_id == "":
        logger.error("Missing required headers: timeZoneId or deviceSn or sessionid")
        raise HTTPException(status_code=400, detail="Missing required headers: timeZoneId or deviceSn")
    logger.info(f"coz_meals_receive {deviceSn} request: {request}")

    conversation_histories.setdefault(deviceSn + "_meals_" + session_id, [SystemMessage(content=SYSTEM_PROMPT)])

    # 加载历史
    history = conversation_histories.get(deviceSn + "_meals_" + session_id, [])
    # history = dicts_to_history(raw_history_dicts)
    # 构造 GraphState
    graph_state: CozMealsGraphState = {"messages": list(history), "mealDate": "", "mealKind": "", "mealNames": []}

    full_messages = initSystemPrompt(history, deviceSn, graph_state, request, timezone, session_id)
    if full_messages is None:
        return "error"

    async def stream_generator():
        global current_meal
        try:
            ai_content = ""
            xml_content = ""
            # 用于存储情况三的mealNames
            mealNames_dict = []
            for chunk, metadata in cozMealsAgent.stream({"messages": full_messages}, stream_mode="messages"):
                # logger.info("chunk: ", chunk)
                # print("chunk:>>>> ", chunk)
                # print("metadata:>>>> ", metadata)
                # print("graph_state:>>>> ", conversation_histories.get(deviceSn + "_" + session_id + "_meals_tools"))
                # logger.info("metadata: ", metadata)
                if chunk.content is None or chunk.content.strip() == "":
                    continue
                # if chunk.content contain 'tool_call'
                # check chunk is toolMessage

                # if isinstance(chunk, ToolMessage):
                #     # if hasattr(chunk, "tool_call_params"):
                #     # print("tool_call_collect_coz_meals_info: ", chunk)
                #     tool_call_data = json.loads(chunk.content)  # 将字符串转为字典
                #     tool_call_params = tool_call_data.get("tool_call_params", {})
                #     if chunk.name == "get_food_suggestion":
                #         tool_call_params = tool_call_data
                #     # 将工具调用的内容转换为字典格式
                #     cardData = {"responseDataClassify": conversation_histories.get(deviceSn + "_" + session_id + "_meals_tools"), "toolname": chunk.name, "responseData": tool_call_params}
                #     # 将工具调用的字典内容作为 SSE 事件发送
                #     confirm_event = {"data": json.dumps(cardData, ensure_ascii=False)}
                #     yield f"data: {confirm_event['data']}\n\n"
                # if not conversation_histories.get(deviceSn + "_" + session_id + "_meals_tools"):
                ai_content += chunk.content
                xml_content += chunk.content

                # print(chunk.content, end="", flush=True)

                # 情况一、二、四
                meal_dict = {}
                if "<meal>" in xml_content:
                    xml_content = xml_content[xml_content.index("<meal>") :]
                # 结束标签
                if "</meal>" in xml_content:
                    xml_content = xml_content[: xml_content.index("</meal>") + len("</meal>")]
                    meal_dict = parse(xml_content)
                if meal_dict:
                    print("meal_dict: ", meal_dict)
                    # 把当前用户饮食日程信息(<meal>块)数据保存到全局变量
                    meal_dict = json.loads(meal_dict.get("meal", ""))
                    current_meal["mealDate"] = meal_dict.get("mealDate", "")
                    current_meal["mealKind"] = meal_dict.get("mealKind", "")
                    current_meal["mealNames"] = meal_dict.get("mealNames", [])

                    # 保存mealNames对应的菜品图片和菜品ID到 meal_dict
                    for mealName in meal_dict.get("mealNames", []):
                        if mealName not in current_meal_dict:
                            food_suggestion = get_food_suggestion(foodName=mealName, mealKind=graph_state["mealKind"], mealDate=graph_state["mealDate"], deviceSn=deviceSn, session_id=session_id)
                            suggest_meals: MealNameType = food_suggestion["existsMealName"]
                            print("mealNames: ", suggest_meals)
                            for suggest_meal in suggest_meals:
                                current_meal_dict[mealName] = {"img": suggest_meal["img"], "mealUid": suggest_meal["mealUid"]}
                                if mealName in current_meal["mealNames"]:
                                    current_meal["mealNames"].remove(mealName)
                                current_meal["mealNames"].append(suggest_meal["mealName"])

                # 情况三 调用工具 get_food_suggestion， 给出菜品建议
                if "<mealNames>" in xml_content:
                    xml_content = xml_content[xml_content.index("<mealNames>") :]
                if "</mealNames>" in xml_content:
                    xml_content = xml_content[: xml_content.index("</mealNames>") + len("</mealNames>")]
                    mealNames_dict = parse(xml_content)

                # 返回自然语言chunk流
                messageData = {"responseDataClassify": "showChatMessage", "responseData": chunk.content}
                chat_event = {"data": json.dumps(messageData, ensure_ascii=False)}
                yield f"data: {chat_event['data']}\n\n"

            # 情况三 调用工具 get_food_suggestion ，返回工具执行结果
            if mealNames_dict and isinstance(mealNames_dict, list):
                # 如果AI返回的 mealNames 为新的，就调用工具 get_food_suggestion ，替换为新菜品
                new_mealNames = []
                for mealName in mealNames_dict["mealNames"]:
                    if mealName not in current_meal["meal_dict"]:
                        food_suggestion = get_food_suggestion(foodName=mealName, mealKind=graph_state["mealKind"], mealDate=graph_state["mealDate"], deviceSn=deviceSn, session_id=session_id)
                        mealNames: MealNameType = food_suggestion["existing_meals"]
                        print("mealNames: ", mealNames)
                    new_mealNames.extend(mealNames)
                new_mealNames = [{"mealName": mealName, "img": current_meal_dict[mealName]["img"], "mealUid": current_meal_dict[mealName]["mealUid"]} if mealName in current_meal_dict else {"mealName": mealName, "img": "", "mealUid": ""} for mealName in new_mealNames]
                # 将工具调用的内容转换为字典格式
                cardData = {"responseDataClassify": "food_suggestion", "responseData": new_mealNames}
                print("cardData: ", cardData)
                # 将工具调用的字典内容作为SSE事件发送
                confirm_event = {"data": json.dumps(cardData, ensure_ascii=False)}
                yield f"data: {confirm_event['data']}\n\n"

            # print last message
            # logger.info(full_messages[-1])
        # 4. 整个流程结束后，发送 'end' 事件
        # yield "event: end\ndata: {}\n\n"
        finally:
            # 保存最终的、完整的历史记录
            logger.info(f"\n[Session: {session_id}] v2 Stream finished. Saving complete history.")
            history = conversation_histories[deviceSn + "_meals_" + session_id]
            history.extend(full_messages)
            history.append(AIMessage(content=ai_content))
            for msg in history[-2:]:
                msg.pretty_print()
            conversation_histories[deviceSn + "_meals_" + session_id] = history
            # conversation_histories[deviceSn + "_meals_" + session_id] = history_to_dicts(full_messages)
            conversation_histories[deviceSn + "_" + session_id + "_chore_tools"] = []

    return StreamingResponse(stream_generator(), media_type="text/event-stream")

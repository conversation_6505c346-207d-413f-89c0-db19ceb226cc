import os
from logger import logger
from langchain_openai import ChatOpenAI
# from langchain.chat_models import init_chat_model
from dotenv import load_dotenv

load_dotenv()


# def openai_gpt():
#     provider = 'openai'
#     # 确保使用支持视觉的模型
#     model = 'gpt-4o'  # 使用 gpt-4o-mini 模型
#     llm = init_chat_model(model, model_provider=provider,temperature=0.7)
#     logger.info(f"coz LLM Initiating openai_gpt model")
#     return llm

def qwen_max():
    llm = ChatOpenAI(

        api_key=os.getenv("DASHSCOPE_APIKEY"),
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        model="qwen-max",
        verbose=True,
    )
    logger.info(f"coz LLM Initiating qwen_max model")
    return llm
def coz_llm_model():
    # 使用通义千问模型
    #llm = azure_openai()
    #llm = openai_gpt()
    #llm = aws_claude()
    #llm = gemini_openai()
    #llm = aws_claude()
    llm=qwen_max()
    return llm

# def aws_claude():
#     llm = ChatBedrockConverse(
#         model=os.environ["AWS_MODEL"],
#         region_name = os.environ["AWS_REGION"],
#         temperature=0.2,
#         max_tokens=None,
#         aws_access_key_id=os.environ["AWS_KEYID"],
#         aws_secret_access_key=os.environ["AWS_KEY"],
#     )
#     logger.info(f"LLM Initiating aws_claude model")
#     return llm
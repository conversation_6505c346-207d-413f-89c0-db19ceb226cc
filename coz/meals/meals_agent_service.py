from typing import TypedDict

import datetime
import json
from typing import Annotated, TypedDict, List
from langchain_core.messages import BaseMessage, AIMessage
import operator
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import ToolNode
from pydantic import BaseModel
from coz.conversation_store import meals_conversation_histories as conversation_histories

from langchain_core.tools import tool

from logger import logger

# This will store simple lists ofrom coz.conversation_store import meals_conversation_histories as   conversation_histories
from coz.cozllm import coz_llm_model

llm = coz_llm_model()


class MealNameType(TypedDict):
    mealName: str
    mealUid: str
    img: str


class CozMealsGraphState(TypedDict):
    """
    图的状态定义
    Attributes:
        messages: 对话消息列表，会通过 operator.add 累加
    """

    messages: Annotated[List[BaseMessage], operator.add]
    error_msg: Annotated[str, operator.add]  # 用于存储错误信息
    tool_call: Annotated[bool, operator.add]
    tool_call_params: Annotated[BaseModel, operator.add]  # 用于标记是否需要调用工具
    intendMeal: str
    selectedMealNames: list
    existsMealName: list
    mealKind: str
    mealDate: str
    mealNames: list[MealNameType]
    meal_dict: dict
    deviceSn: str
    laster_tool_call: str


# @tool(name_or_callable="get_food_suggestion",
#       description="Search for existing meals based on user's intended meal name. Use this when user mentions a specific meal they want to arrange.")
def get_food_suggestion(foodName: str, mealKind: str, mealDate: str, deviceSn: str, session_id: str):
    """Search for existing meals based on user intent.
    Args:
            foodName: str, the meal name or food the user wants to arrange
            deviceSn: The current deviceSn,
            session_id: str,
            timezone: str
    """

    # logger.info(f"get_food_suggestion tool called with userIntent: {foodName}")
    conversation_histories[deviceSn + "_" + session_id + "_meals_tools"] = ["get_food_suggestion"]

    # 模拟搜索逻辑 - 这里可以连接实际的数据库或API
    if foodName == "鸡肉":
        existing_meals: list[MealNameType] = [
            {"mealName": "宫保鸡丁", "mealUid": "meal12345", "img": "https://www.baidu.com/"},
        ]
    elif foodName == "牛肉":
        existing_meals: list[MealNameType] = [
            {"mealName": "兰州牛肉面", "mealUid": "meal12345", "img": "https:"},
        ]
    elif foodName == "猪肉":
        existing_meals: list[MealNameType] = [
            {"mealName": "红烧肉", "mealUid": "meal12345", "img": "https:"},
        ]
    elif foodName == "羊肉":
        existing_meals: list[MealNameType] = []
    else:
        existing_meals: list[MealNameType] = [
            {"mealName": "Tomato pizza", "mealUid": "meal12345", "img": "https:"},
            {"mealName": "beef pizza", "mealUid": "meal67890", "img": "https:"},
        ]

    result = {"searchQuery": foodName, "existsMealName": existing_meals, "mealKind": mealKind, "mealDate": mealDate, "laster_tool_call": "get_food_suggestion"}

    # Store the tool result message in conversation history
    tool_message = {"type": "tool_result", "tool_name": "get_food_suggestion", "content": result, "timestamp": datetime.datetime.now().isoformat()}

    # Get existing history or create new list
    history_key = deviceSn + "_meals_" + session_id
    if history_key not in conversation_histories:
        conversation_histories[history_key] = []

    conversation_histories[history_key].append(tool_message)

    return result


def get_daily_meals(mealKind: str, mealDate: str, deviceSn: str, session_id: str, timezone: str) -> dict:
    daily_kind_meals = {
        "mealDate": mealDate,
        "mealKind": mealKind,
        "deviceSn": deviceSn,
        "session_id": session_id,
        "timezone": timezone,
        "meals": [
            {
                "mealName": "Tomato pizza",
                "mealUid": "meal12345",
            },
            {
                "mealName": "beef pizza",
                "mealUid": "meal67890",
            },
        ],
    }
    return daily_kind_meals


# 2. 定义我们的自定义工具
@tool(name_or_callable="collect_coz_meals_info", description="coz_meals_tools")
def collect_coz_meals_info(intendMeal: list, mealKind: str, mealDate: str, selectedMealNames, deviceSn: str, session_id: str, timezone: str) -> dict:
    """check if the chore information is complete.
    Args:
            intendMeal: str
            selectedMealNames: list,
            mealKind: str,
            mealDate: str,
            deviceSn: str,
            session_id: str,
            timezone: str
    """
    # logger.info("collect_coz_meals_info tool called with parameters:")
    conversation_histories[deviceSn + "_" + session_id + "_meals_tools"] = ["collect_coz_meals_info"]

    result = {"tool_call": True, "tool_call_params": {"intendMeal": intendMeal, "mealKind": mealKind, "mealDate": mealDate, "deviceSn": deviceSn, "selectedMealNames": selectedMealNames, "title": "Here's a preview of your chore."}}

    # Store the tool result message in conversation history
    tool_message = {"type": "tool_result", "tool_name": "collect_coz_meals_info", "content": result, "timestamp": datetime.datetime.now().isoformat()}

    # Get existing history or create new list
    history_key = deviceSn + "_" + session_id + "_meals_tools"
    if history_key not in conversation_histories:
        conversation_histories[history_key] = []

    conversation_histories[history_key].append(tool_message)

    return result


# meals_entity_tools = [collect_coz_meals_info,get_food_suggestion]

# model_with_tools= llm.bind_tools(meals_entity_tools)
model_with_tools = llm

get_food_suggestion_node = ToolNode([get_food_suggestion])

# collect_coz_meals_info_node = ToolNode([collect_coz_meals_info])


# 定义 Agent 节点
def meals_agent_node(state: CozMealsGraphState):
    """
    Agent 节点：调用 LLM 来决定下一步行动
    """
    logger.info("\n[节点]: Agent 运行中...")
    response = model_with_tools.invoke(state["messages"])
    # 将 LLM 的响应（可能是普通消息，也可能是工具调用请求）添加到状态中
    return {"messages": [response]}


def should_continue(state: CozMealsGraphState) -> str:
    """
    条件边：决定是继续调用工具还是结束流程
    """
    # logger.info("\n state in should_continue: ", state)
    # logger.info("\n[判断]: 检查是否需要调用工具...")
    last_message = state["messages"][-1]
    # Step 1: 检查是否有 tool_call

    # if state["selectedMealNames"]  is not None:
    #    return "meals_agent"

    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        # print("  - 结论: 是，需要调用工具。", last_message.tool_calls)
        # print(f'last_message.tool_calls>>>>{last_message.tool_calls}')
        tool_calls = last_message.tool_calls
        toolName = tool_calls[0].get("name")
        toolArgs = tool_calls[0].get("args")
        # logger.info("toolArgs>>>",toolArgs)
        # conversation_histories[deviceSn + "_meals_selectedMealNames" + session_id]
        # if "laster_tool_call" in state and state["laster_tool_call"] =='get_meal_exists':
        #     return END
        # if "selectMealNames" in state and state["selectMealNames"] is not None:
        #     return "meals_confirm_node"
        # lasttool = last_message.tool_calls
        return toolName  # 路由到工具节点
    else:
        # logger.info("  - 结论: 否，流程结束。")
        return END  # 结束流程


# 创建 StateGraph 对象，并传入我们定义的状态
workflow = StateGraph(CozMealsGraphState)
# 添加节点
workflow.add_node("meals_agent", meals_agent_node)
workflow.add_node("get_food_suggestion_node", get_food_suggestion_node)

# workflow.add_node("meals_confirm_node", meals_confirm_node)
# 设置入口点
workflow.set_entry_point("meals_agent")
# 添加条件边
workflow.add_conditional_edges(
    "meals_agent",  # 从 agent 节点出发
    should_continue,  # 使用 should_continue 函数做判断
    {
        # "meals_confirm_node":"meals_confirm_node",
        "get_food_suggestion": "get_food_suggestion_node",  # 如果返回 "tools"，则流向 tools 节点
        "meals_agent": "meals_agent",  # 如果返回 "tools"，则流向 tools 节点
        END: END,  # 如果返回 END，则结束
    },
)

# 添加从工具节点回到 Agent 节点的边
# 这样，工具执行完后，结果会返回给 Agent，让它做下一步的总结回复
workflow.add_edge("meals_agent", END)
workflow.add_edge("get_food_suggestion_node", END)


# 编译图，生成可运行的应用
cozMealsAgent = workflow.compile()
# cozMealsAgent.get_graph(xray=True).draw_mermaid_png(output_file_path="cozMealsAgent_v5.png")

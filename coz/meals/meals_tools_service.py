import pydantic
from langchain_core.tools import tool
from coz.conversation_store import meals_conversation_histories as   conversation_histories
from logger import logger










def business_error(error_msg:str) -> dict:
    """
    简单返回错误,只返回 error_msg信息
    """
    logger.info(f"member error get msg:{error_msg}")
    # 打印错误信息（模拟日志）
    logger.info(f"  - 错误详情: {error_msg}")
    return {"error_msg": "error msg from business_error"}


# 将我们定义的工具放入一个列表
# 将我们定义的工具放入一个列表







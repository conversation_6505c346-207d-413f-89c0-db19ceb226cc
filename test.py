import requests
import json
import argparse
parser = argparse.ArgumentParser()
parser.add_argument('--msg', type=str)
args = parser.parse_args()

url = "http://127.0.0.1:8002/cal-server/ai/agents/coz_meals_steam"
headers = {"Content-Type": "application/json", "timeZoneId": "Asia/Shanghai", "deviceSn": "testDevice001", "sessionid": "testSession007"}
data = {
    # "message": "你能干什么",
    "message": args.msg,
    # "selectedMealNames": ["苹果派"]
}

response = requests.post(url, json=data, headers=headers, stream=True)
buffer = b""
for chunk in response.iter_content(chunk_size=1):
    if not chunk:
        continue
    buffer += chunk
    while b"\n" in buffer:
        line, buffer = buffer.split(b"\n", 1)
        if not line:
            continue
        try:
            data_json = line.decode("utf-8").strip()
            if not data_json.startswith("data: "):
                continue
            payload = data_json[len("data: "):]
            obj = json.loads(payload)
            print(obj.get("responseData", ""), end="", flush=True)
        except Exception as e:
            print(f"解析失败: {e}")
print()


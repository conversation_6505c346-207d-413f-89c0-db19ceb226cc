FROM python:3.13.2-slim
# Set working directory
WORKDIR /home

# Set non-sensitive environment variables
ARG APP_ENV=production
ARG POSTGRES_URL

ENV APP_ENV=${APP_ENV} \
    PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100 \
    POSTGRES_URL=${POSTGRES_URL}

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    && pip install --upgrade pip \
    && pip install -U langgraph-cli[inmem] \
    && pip install -U langgraph langchain-community langchain-anthropic tavily-python pandas langchain-openai gradio \
    && pip install --upgrade --quiet langchain_aws \
    && pip install -U langchain-google-genai \
    && pip install fastapi uvicorn \
    && rm -rf /var/lib/apt/lists/*

COPY  ./ /home

# Default port
EXPOSE 8001

# Log the environment we're using
RUN echo "Using ${APP_ENV} environment"

# Command to run the application
CMD ["python", "main.py", "--port", "8001"]
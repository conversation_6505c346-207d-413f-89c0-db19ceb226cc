from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Request
from starlette.responses import StreamingResponse

from logger import logger
from pydantic import BaseModel
from langchain_core.messages import ToolMessage
from utils import _print_output, display_arrangement


router = APIRouter()

# 定义请求体模型
class ChatRequest(BaseModel):
    message: str

    try:
        logger.info(f"Received message: {request.message}")

        config = {
        "configurable": {
            "passenger_id": request.u_id,
            "thread_id": request.device_id,
        }
    }
        
        res = process_message(request.message, config)
        logger.info(f"Response: {res}")
            
        return {"message": res}
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/check_status")
def check_status():
    return {"status": "ok"}



    


# cozAgents

pip install --upgrade pip 
pip install -U langgraph-cli[inmem] 
pip install -U langgraph langchain-community langchain-anthropic tavily-python pandas langchain-openai gradio 
pip install --upgrade --quiet langchain_aws 
pip install fastapi uvicorn 

python main.py --port 8002

curl --location --request POST 'http://127.0.0.1:8001/cal-server/ai/agents/coz_meals' \
--header 'deviceSn: EW40000OC' \
--header 'timeZoneId: Asia/Shanghai' \
--header 'sessionid: abc11144455' \
--header 'Content-Type: application/json' \
--data-raw '{
    "message": "i want pizza for tomorrow lunch"
    
}'









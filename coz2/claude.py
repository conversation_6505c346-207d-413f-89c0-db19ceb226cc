import os
from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT

# 设置API凭证（从环境变量或直接设置）
os.environ["ANTHROPIC_API_KEY"] = "你的_SecretAccessKey"  # 替换为你的SecretAccessKey

# 初始化Anthropic客户端
anthropic = Anthropic()

def call_claude(prompt: str, max_tokens_to_sample: int = 1000) -> str:
    """
    使用Claude模型生成文本
    
    参数:
        prompt: 用户输入的提示
        max_tokens_to_sample: 最大生成token数
    
    返回:
        Claude生成的文本
    """
    # 构建完整的对话格式（<PERSON>要求格式：HUMAN_PROMPT + 提示 + AI_PROMPT）
    full_prompt = f"{HUMAN_PROMPT} {prompt}{AI_PROMPT}"
    
    # 调用Claude API
    response = anthropic.completions.create(
        model="claude-3-opus-20240602",  # 指定Claude模型版本
        prompt=full_prompt,
        max_tokens_to_sample=max_tokens_to_sample,
    )
    
    return response.completion

# 使用示例
if __name__ == "__main__":
    user_prompt = "请介绍一下量子计算的基本原理"
    response = call_claude(user_prompt)
    print(f"用户问题: {user_prompt}")
    print(f"Claude回答: {response}")



# def chat_with_claude():
#     """演示与Claude的多轮对话"""
#     messages = []
#     print("开始与Claude对话（输入'退出'结束）")
    
#     while True:
#         user_input = input("你: ")
#         if user_input.lower() == "退出":
#             break
            
#         # 添加用户消息到对话历史
#         messages.append({"role": "user", "content": user_input})
        
#         # 构建完整prompt
#         prompt = ""
#         for msg in messages:
#             if msg["role"] == "user":
#                 prompt += f"{HUMAN_PROMPT} {msg['content']}"
#             else:  # assistant
#                 prompt += f"{AI_PROMPT} {msg['content']}"
        
#         # 添加AI响应标记
#         prompt += AI_PROMPT
        
#         # 调用API
#         response = anthropic.completions.create(
#             model="claude-3-opus-20240602",
#             prompt=prompt,
#             max_tokens_to_sample=1000,
#         )
        
#         # 保存AI回复
#         ai_response = response.completion
#         messages.append({"role": "assistant", "content": ai_response})
        
#         print(f"Claude: {ai_response}")

# # 运行多轮对话
# chat_with_claude()
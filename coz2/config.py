import os
from typing import Dict, List
from datetime import datetime
from dotenv import load_dotenv
load_dotenv()

# 全局配置
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "your-openai-api-key")
OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

# 全局存储 - 模拟数据库
class GlobalStorage:
    def __init__(self):
        # 用户自定义餐食数据库 (最多100个)
        self.custom_meals: Dict[str, Dict] = {}
        
        # 系统预设餐食数据库
        self.preset_meals: Dict[str, Dict] = {
            "Everything Avocado Toast": {"ingredients": ["avocado", "bread", "everything seasoning"]},
            "Avocado and Salmon Salad": {"ingredients": ["avocado", "salmon", "lettuce"]},
            "Whole-Grain Toast with Avocado and Fried Egg": {"ingredients": ["avocado", "bread", "egg"]},
            "Mashed Potatoes": {"ingredients": ["potatoes", "butter", "milk"]},
            "Roasted Vegetables": {"ingredients": ["mixed vegetables", "olive oil"]},
            "Raspberry Pie": {"ingredients": ["raspberries", "flour", "sugar"]},
            "Caesar Salad": {"ingredients": ["lettuce", "caesar dressing", "croutons"]},
            "Grilled Chicken": {"ingredients": ["chicken breast", "herbs", "oil"]},
        }
        
        # 用户餐食计划 (每餐最多50道菜)
        self.meal_plans: Dict[str, List[str]] = {}
        
        # 会话历史
        self.conversation_history: Dict[str, List] = {}

# 全局实例
storage = GlobalStorage()

# 餐食类型映射
MEAL_KIND_MAPPING = {
    "breakfast": ["morning", "早上", "早餐", "7am", "8am", "9am"],
    "lunch": ["noon", "中午", "午餐", "12pm", "1pm"],
    "dinner": ["evening", "night", "晚上", "晚餐", "6pm", "7pm", "8pm"],
    "snack": ["snack", "零食", "加餐"]
}

# 日期关键词映射
DATE_KEYWORDS = {
    "today": 0,
    "tomorrow": 1,
    "yesterday": -1,
    "monday": "monday",
    "tuesday": "tuesday", 
    "wednesday": "wednesday",
    "thursday": "thursday",
    "friday": "friday",
    "saturday": "saturday",
    "sunday": "sunday"
}



from typing import TypedDict, List, Optional, Dict, Any, Annotated
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from datetime import datetime, timedelta
import json
import re
import operator
from config import storage, MEAL_KIND_MAPPING, DATE_KEYWORDS, OPENAI_API_KEY, OPENAI_BASE_URL

class MealState(TypedDict):
    """餐食状态定义"""
    messages: Annotated[List[BaseMessage], operator.add]
    meal_name: Optional[str]
    meal_kind: Optional[str]
    meal_date: Optional[str]
    exist: Optional[bool]
    search_results: Optional[List[str]]
    selected_meals: Optional[List[str]]
    session_id: str
    current_step: str

class CozyMealAgent:
    def __init__(self):
        # 初始化LLM - 添加超时设置
        self.llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0.7,
            api_key=OPENAI_API_KEY,
            base_url=OPENAI_BASE_URL,
            timeout=10,  # 设置10秒超时
            max_retries=1  # 最多重试1次
        )
        
        # 创建状态图
        self.workflow = self._create_workflow()
        self.memory = MemorySaver()
        self.app = self.workflow.compile(checkpointer=self.memory)
        
        # 画图
        # self.app.get_graph(xray=True).draw_mermaid_png(output_file_path="meal_agent.png")
    
    def _create_workflow(self) -> StateGraph:
        """创建LangGraph工作流"""
        workflow = StateGraph(MealState)

        # 添加主要的agent节点
        workflow.add_node("agent", self._agent_node)
        workflow.add_node("add_meal", self._add_meal_node)

        # 设置入口点
        workflow.set_entry_point("agent")

        # 添加条件边
        workflow.add_conditional_edges(
            "agent",
            self._should_continue,
            {
                "add_meal": "add_meal",
                "end": END
            }
        )

        # 添加从add_meal回到END的边
        workflow.add_edge("add_meal", END)

        return workflow
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """# 饮食日程助手

## 角色设定

你是一个聪明的厨师助手，用户称呼你为 Cozy，负责帮助用户安排餐食。

## 交互规则

- 根据当前饮食日程信息，结合以下场景进行交互。
- mealDate 格式为 YYYY-MM-DD。
- mealKind 可选值： "breakfast" | "lunch" | "dinner" | "snack"。
  - Snack: can be any time of the day, user have to say it explicitly
  - Breakfast: morning, 06:00 - 10:00
  - Lunch: noon, 11:00 - 14:00
  - Dinner: Evening, night, 18:00 - 24:00

## 响应格式

当用户提供餐食信息时，你需要：
1. 提供自然语言回复
2. 在回复中包含结构化的餐食信息

示例回复格式：
好的，我来帮你安排牛油果吐司作为早餐。

<meal>
{
  "meal_name": "avocado toast",
  "meal_kind": "breakfast",
  "meal_date": "2025-07-29",
  "intent": "add_meal"
}
</meal>

## 情况处理

### 情况一：用户提供完整信息
用户说："I want avocado toast for breakfast today"
回复：好的，我来帮你安排今天的早餐。

<meal>
{
  "meal_name": "avocado toast",
  "meal_kind": "breakfast",
  "meal_date": "2025-07-29",
  "intent": "add_meal"
}
</meal>

### 情况二：信息不完整
如果用户只说："I want some chicken"
回复：好的，你想要鸡肉。请问这是什么时候的餐食呢？是早餐、午餐、晚餐还是加餐？

### 情况三：确认添加
如果用户说："confirm" 或 "yes"
回复：太好了！你的餐食已经添加到计划中。祝用餐愉快！

请根据用户输入提供友好的回复和相应的餐食信息。"""

    def _agent_node(self, state: MealState) -> MealState:
        """主要的Agent节点 - 使用智能回复系统"""
        user_message = state["messages"][-1].content

        # 优先使用智能备用回复，确保稳定性
        response_text = self._generate_fallback_response(user_message, state)
        response = AIMessage(content=response_text)
        self._parse_llm_response(response_text, state)

        return {"messages": [response]}

    def _generate_fallback_response(self, user_message: str, state: MealState) -> str:
        """生成智能备用回复"""
        user_lower = user_message.lower()

        # 检测餐食类型
        meal_foods = {
            "avocado": "牛油果吐司",
            "chicken": "鸡肉",
            "beef": "牛肉",
            "pork": "猪肉",
            "fish": "鱼肉",
            "salad": "沙拉",
            "pizza": "披萨",
            "pasta": "意大利面",
            "rice": "米饭",
            "noodles": "面条",
            "soup": "汤",
            "sandwich": "三明治"
        }

        # 检测时间类型
        meal_times = {
            "breakfast": "早餐",
            "lunch": "午餐",
            "dinner": "晚餐",
            "snack": "加餐"
        }

        detected_food = None
        detected_time = None

        for eng, chi in meal_foods.items():
            if eng in user_lower:
                detected_food = eng
                break

        for eng, chi in meal_times.items():
            if eng in user_lower or any(word in user_lower for word in ["morning", "noon", "evening", "night"]):
                detected_time = eng
                break

        # 确认相关回复
        if "confirm" in user_lower or "yes" in user_lower:
            return "太好了！你的餐食已经添加到计划中。祝用餐愉快！"

        # 根据检测到的信息生成回复
        if detected_food and detected_time:
            return f"好的！我来帮你安排{meal_foods[detected_food]}作为{meal_times[detected_time]}。\n\n<meal>\n{{\n  \"meal_name\": \"{detected_food}\",\n  \"meal_kind\": \"{detected_time}\",\n  \"meal_date\": \"2025-07-29\"\n}}\n</meal>"
        elif detected_food:
            return f"好的，你想要{meal_foods[detected_food]}。请问这是什么时候的餐食呢？是早餐、午餐、晚餐还是加餐？"
        elif detected_time:
            return f"好的，{meal_times[detected_time]}。请问你想吃什么呢？"
        else:
            return "你好！我是Cozy，你的餐食助手。请告诉我你想要什么餐食，什么时候吃？"

    def _parse_llm_response(self, response_content: str, state: MealState):
        """解析LLM回复中的结构化信息"""
        try:
            # 查找<meal>标签中的JSON数据
            meal_start = response_content.find('<meal>')
            meal_end = response_content.find('</meal>')

            if meal_start != -1 and meal_end != -1:
                meal_json_str = response_content[meal_start + 6:meal_end].strip()
                meal_data = json.loads(meal_json_str)

                # 更新状态
                state.update({
                    "meal_name": meal_data.get("meal_name"),
                    "meal_kind": meal_data.get("meal_kind"),
                    "meal_date": meal_data.get("meal_date"),
                    "current_step": "parsed"
                })

                # 如果有餐食名称，进行搜索
                if meal_data.get("meal_name"):
                    self._search_existing_meals(meal_data.get("meal_name"), state)

        except (json.JSONDecodeError, Exception) as e:
            # 如果解析失败，使用备用方法提取信息
            user_message = state["messages"][-2].content.lower() if len(state["messages"]) >= 2 else ""
            state.update({
                "meal_name": self._extract_meal_name(user_message),
                "meal_kind": self._extract_meal_kind(user_message),
                "meal_date": self._extract_meal_date(user_message),
                "current_step": "fallback_parsed"
            })

    def _search_existing_meals(self, meal_name: str, state: MealState):
        """搜索现有餐食"""
        search_results = []
        meal_name_lower = meal_name.lower()

        # 在预设餐食中搜索
        for preset_meal in storage.preset_meals.keys():
            if any(keyword in preset_meal.lower() for keyword in meal_name_lower.split()):
                search_results.append(preset_meal)

        # 在自定义餐食中搜索
        for custom_meal in storage.custom_meals.keys():
            if any(keyword in custom_meal.lower() for keyword in meal_name_lower.split()):
                search_results.append(custom_meal)

        state["search_results"] = search_results
        state["exist"] = len(search_results) > 0

    def _should_continue(self, state: MealState) -> str:
        """决定是否继续到下一步"""
        last_message = state["messages"][-1]

        # 检查用户是否确认添加餐食
        if len(state["messages"]) >= 2:
            user_message = state["messages"][-2].content.lower()
            if "confirm" in user_message or "yes" in user_message:
                return "add_meal"

        # 检查是否有完整的餐食信息需要添加
        if (state.get("meal_name") and
            state.get("meal_kind") and
            state.get("meal_date") and
            "confirm" in last_message.content.lower()):
            return "add_meal"

        # 默认结束对话
        return "end"

    def _extract_meal_name(self, text: str) -> Optional[str]:
        """从文本中提取餐食名称"""
        # 简化实现：查找常见餐食关键词
        meal_keywords = ["avocado", "toast", "salad", "pie", "chicken", "potato"]
        for keyword in meal_keywords:
            if keyword in text:
                return keyword
        return None
    
    def _extract_meal_kind(self, text: str) -> Optional[str]:
        """从文本中提取餐食类型"""
        for kind, keywords in MEAL_KIND_MAPPING.items():
            for keyword in keywords:
                if keyword in text:
                    return kind
        return None
    
    def _extract_meal_date(self, text: str) -> Optional[str]:
        """从文本中提取日期"""
        today = datetime.now()
        
        if "today" in text:
            return today.strftime("%Y-%m-%d")
        elif "tomorrow" in text:
            return (today + timedelta(days=1)).strftime("%Y-%m-%d")
        elif "monday" in text:
            return self._get_next_weekday(today, 0).strftime("%Y-%m-%d")
        # 添加更多日期解析逻辑...
        
        return None
    
    def _get_next_weekday(self, date: datetime, weekday: int) -> datetime:
        """获取下一个指定星期几的日期"""
        days_ahead = weekday - date.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        return date + timedelta(days_ahead)
    
    def _search_meals_node(self, state: MealState) -> MealState:
        """搜索餐食节点"""
        meal_name = state.get("meal_name", "")
        
        # 在预设餐食中搜索
        search_results = []
        for preset_meal, details in storage.preset_meals.items():
            if meal_name.lower() in preset_meal.lower() or any(meal_name.lower() in ing.lower() for ing in details["ingredients"]):
                search_results.append(preset_meal)
        
        # 限制返回前5个结果
        search_results = search_results[:5]
        
        state.update({
            "search_results": search_results,
            "exist": len(search_results) > 0,
            "current_step": "searched"
        })
        
        return state
    
    def _collect_info_node(self, state: MealState) -> MealState:
        """收集信息节点"""
        meal_name = state.get("meal_name")
        meal_kind = state.get("meal_kind") 
        meal_date = state.get("meal_date")
        search_results = state.get("search_results", [])
        
        # 生成响应消息
        if not meal_name:
            response = "What would you like to eat?"
        elif not meal_date and not meal_kind:
            response = "When would you like to plan this meal?"
        elif not meal_kind:
            response = "Is this for breakfast, lunch, dinner, or a snack?"
        elif not meal_date:
            response = "Which day would you like this meal for?"
        elif search_results:
            response = f"Got it! We have {', '.join(search_results)} available for you. Which one would you like?"
        else:
            response = f"Got it, but '{meal_name}' is not in our database. Do you want me to create the meal first, then add it?"
        
        state["messages"].append(AIMessage(content=response))
        state["current_step"] = "collected"
        
        return state
    
    def _confirm_meal_node(self, state: MealState) -> MealState:
        """确认餐食节点"""
        meal_name = state.get("meal_name")
        meal_kind = state.get("meal_kind")
        meal_date = state.get("meal_date")
        
        # 生成确认消息
        confirmation = f"Awesome! The scheduled meal details are listed here:\n"
        confirmation += f"Meal: {meal_name}\n"
        confirmation += f"Type: {meal_kind}\n" 
        confirmation += f"Date: {meal_date}\n"
        confirmation += "Would you like to confirm?"
        
        state["messages"].append(AIMessage(content=confirmation))
        state["current_step"] = "confirmed"
        
        return state
    
    def _add_meal_node(self, state: MealState) -> MealState:
        """添加餐食节点"""
        meal_name = state.get("meal_name")
        meal_kind = state.get("meal_kind")
        meal_date = state.get("meal_date")
        exist = state.get("exist", False)
        session_id = state.get("session_id", "default")
        
        # 检查限制
        if not exist and len(storage.custom_meals) >= 100:
            response = "You've created 100 personalized meals – that's the limit for now! To manage your recipes, go to My Meals."
            state["messages"].append(AIMessage(content=response))
            return state
        
        meal_key = f"{meal_date}_{meal_kind}"
        current_meals = storage.meal_plans.get(meal_key, [])
        
        if len(current_meals) >= 50:
            response = "Your meal plan is looking great with 50 dishes already. To add anything new, please remove a few first."
            state["messages"].append(AIMessage(content=response))
            return state
        
        # 添加餐食
        try:
            if not exist:
                # 创建自定义餐食
                storage.custom_meals[meal_name] = {"created_by": session_id, "date_created": datetime.now().isoformat()}
            
            # 添加到计划
            if meal_key not in storage.meal_plans:
                storage.meal_plans[meal_key] = []
            storage.meal_plans[meal_key].append(meal_name)
            
            response = "Your meal has been added. Enjoy!"
            
        except Exception as e:
            response = "Sorry, there has been something wrong. Would you like to try again?"
        
        state["messages"].append(AIMessage(content=response))
        state["current_step"] = "completed"
        
        return state
    
    def _route_after_analysis(self, state: MealState) -> str:
        """分析后的路由决策"""
        meal_name = state.get("meal_name")
        meal_kind = state.get("meal_kind")
        meal_date = state.get("meal_date")
        
        # 检查用户是否在确认
        last_message = state["messages"][-1].content.lower()
        if "confirm" in last_message or "yes" in last_message:
            return "confirm"
        
        # 如果信息完整且有餐食名称，进行搜索
        if meal_name and not state.get("search_results"):
            return "search"
        
        # 如果信息不完整，收集信息
        if not meal_name or not meal_kind or not meal_date:
            return "collect"
        
        # 默认收集信息
        return "collect"

# 创建全局代理实例
cozy_agent = CozyMealAgent()
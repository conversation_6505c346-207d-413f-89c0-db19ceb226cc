#!/usr/bin/env python3
"""
Cozy餐食助手测试启动脚本
使用方法:
1. python run_tests.py - 运行完整测试套件
2. python run_tests.py interactive - 进入交互模式
3. python run_tests.py "你的消息" - 发送单条消息测试
"""

import subprocess
import sys
import time
import os
import signal
from pathlib import Path

def start_server():
    """启动FastAPI服务器"""
    print("🚀 启动 Cozy 餐食助手服务器...")
    
    # 设置环境变量 (如果没有设置的话)
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  警告: 未设置 OPENAI_API_KEY 环境变量")
        print("请设置你的OpenAI API密钥:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return None
    
    try:
        # 启动服务器进程
        server_process = subprocess.Popen([
            sys.executable, "main.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 检查进程是否还在运行
        if server_process.poll() is None:
            print("✅ 服务器启动成功!")
            return server_process
        else:
            print("❌ 服务器启动失败")
            stdout, stderr = server_process.communicate()
            print(f"错误信息: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")
        return None

def run_tests(args):
    """运行测试"""
    print("🧪 开始运行测试...")
    
    try:
        if args:
            # 传递参数给测试脚本
            subprocess.run([sys.executable, "test.py"] + args, check=True)
        else:
            # 运行默认测试
            subprocess.run([sys.executable, "test.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试运行失败: {e}")
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")

def main():
    """主函数"""
    print("🍽️  Cozy 餐食助手 - 完整测试环境")
    print("=" * 60)
    
    # 检查必要文件是否存在
    required_files = ["main.py", "test.py", "meal_agent.py", "config.py"]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return
    
    # 启动服务器
    server_process = start_server()
    
    if server_process is None:
        print("❌ 无法启动服务器，测试终止")
        return
    
    try:
        # 运行测试
        test_args = sys.argv[1:] if len(sys.argv) > 1 else []
        run_tests(test_args)
        
    finally:
        # 清理：关闭服务器
        print("\n🧹 清理资源...")
        if server_process and server_process.poll() is None:
            print("⏹️  关闭服务器...")
            server_process.terminate()
            
            # 等待进程结束
            try:
                server_process.wait(timeout=5)
                print("✅ 服务器已关闭")
            except subprocess.TimeoutExpired:
                print("⚠️  强制关闭服务器...")
                server_process.kill()
                server_process.wait()
                print("✅ 服务器已强制关闭")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见!")
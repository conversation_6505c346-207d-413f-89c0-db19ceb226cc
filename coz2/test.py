import asyncio
import json
import sys
from typing import Dict, Any
import aiohttp
from datetime import datetime

class CozyTestClient:
    """Cozy餐食助手测试客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session_id = None
    
    async def send_message(self, message: str, selected_meals: list = None) -> Dict[Any, Any]:
        """发送消息并获取流式响应"""
        url = f"{self.base_url}/chat"
        
        payload = {
            "message": message,
            "session_id": self.session_id,
            "selected_meals": selected_meals
        }
        
        print(f"\n🧑 用户: {message}")
        print("🤖 Cozy: ", end="", flush=True)
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        full_response = ""
                        async for line in response.content:
                            line = line.decode('utf-8').strip()
                            if line.startswith('data: '):
                                try:
                                    data = json.loads(line[6:])
                                    
                                    if data.get('type') == 'char':
                                        # 流式显示字符
                                        print(data['char'], end="", flush=True)
                                        full_response += data['char']
                                    
                                    elif data.get('type') == 'complete':
                                        # 完整响应
                                        response_data = data['response']
                                        self.session_id = response_data['session_id']
                                        print()  # 换行
                                        return response_data
                                    
                                    elif data.get('type') == 'error':
                                        print(f"\n❌ 错误: {data['error']}")
                                        return {"error": data['error']}
                                        
                                except json.JSONDecodeError:
                                    continue
                    else:
                        print(f"\n❌ HTTP错误: {response.status}")
                        return {"error": f"HTTP {response.status}"}
                        
        except Exception as e:
            print(f"\n❌ 连接错误: {e}")
            return {"error": str(e)}
    
    async def test_scenario_1(self):
        """测试场景1: 现有菜谱添加成功"""
        print("\n" + "="*50)
        print("📋 测试场景1: 现有菜谱添加成功")
        print("="*50)
        
        # 步骤1: 用户表达想吃牛油果
        await self.send_message("Hi Cozy. I am craving some avocados.")
        
        # 步骤2: 用户提供时间信息
        await self.send_message("Next Monday morning would be great.")
        
        # 步骤3: 用户选择具体菜谱
        await self.send_message("I would like to have the Whole-grain toast with avocado and fried Egg.")
        
        # 步骤4: 用户确认
        await self.send_message("Confirm")
    
    async def test_scenario_2(self):
        """测试场景2: 菜谱不存在，需要先添加"""
        print("\n" + "="*50)
        print("📋 测试场景2: 创建自定义菜谱")
        print("="*50)
        
        # 重置会话
        self.session_id = None
        
        # 步骤1: 用户想要不存在的菜谱
        await self.send_message("Hey Cozy, I'm craving some raspberry pie today")
        
        # 步骤2: 用户提供具体时间
        await self.send_message("I'd like it at 7 am.")
        
        # 步骤3: 用户同意创建
        await self.send_message("Sure.")
        
        # 步骤4: 用户确认
        await self.send_message("Confirm")
    
    async def test_scenario_3(self):
        """测试场景3: 信息不完整的追问"""
        print("\n" + "="*50)
        print("📋 测试场景3: 信息不完整追问")
        print("="*50)
        
        # 重置会话
        self.session_id = None
        
        # 步骤1: 只提供餐食名称
        await self.send_message("I want some chicken")
        
        # 步骤2: 提供时间但不完整
        await self.send_message("Tomorrow")
        
        # 步骤3: 提供完整信息
        await self.send_message("For lunch")
        
        # 步骤4: 选择菜谱
        await self.send_message("I'll take the first one")
        
        # 步骤5: 确认
        await self.send_message("Confirm")
    
    async def test_scenario_4(self):
        """测试场景4: 多个餐食添加"""
        print("\n" + "="*50)
        print("📋 测试场景4: 多个餐食添加")
        print("="*50)
        
        # 重置会话
        self.session_id = None
        
        # 步骤1: 用户想要多个餐食
        await self.send_message("Hey Cozy, help me plan thanksgiving dinner. I'm thinking mashed potatoes, roasted veggies, and raspberry pie for dessert.")
        
        # 步骤2: 用户选择多个
        await self.send_message("I'd like the first one and the third one.")
        
        # 步骤3: 用户添加更多
        await self.send_message("I also want some beers.")
        
        # 步骤4: 确认
        await self.send_message("Confirm")
    
    async def test_error_scenarios(self):
        """测试错误场景"""
        print("\n" + "="*50)
        print("📋 测试场景5: 错误处理")
        print("="*50)
        
        # 重置会话
        self.session_id = None
        
        # 测试无法识别的输入
        await self.send_message("Dhjieowww, eheiwo, ee.")
        
        # 测试正常恢复
        await self.send_message("I want some salad for lunch today")
    
    async def check_server_status(self):
        """检查服务器状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/health") as response:
                    if response.status == 200:
                        print("✅ 服务器运行正常")
                        return True
                    else:
                        print(f"❌ 服务器状态异常: {response.status}")
                        return False
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            print("请确保服务器正在运行: python main.py")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试 Cozy 餐食助手")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查服务器状态
        if not await self.check_server_status():
            return
        
        try:
            # 运行所有测试场景
            await self.test_scenario_1()
            await asyncio.sleep(1)
            
            await self.test_scenario_2()
            await asyncio.sleep(1)
            
            await self.test_scenario_3()
            await asyncio.sleep(1)
            
            await self.test_scenario_4()
            await asyncio.sleep(1)
            
            await self.test_error_scenarios()
            
            print("\n" + "="*50)
            print("✅ 所有测试完成!")
            print("="*50)
            
        except KeyboardInterrupt:
            print("\n\n⏹️  测试被用户中断")
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {e}")

async def main():
    """主函数"""
    print("🍽️  Cozy 餐食助手测试工具")
    print("=" * 50)
    
    # 创建测试客户端
    client = CozyTestClient()
    
    if len(sys.argv) > 1:
        # 交互模式
        if sys.argv[1] == "interactive":
            print("进入交互模式 (输入 'quit' 退出)")
            while True:
                try:
                    user_input = input("\n🧑 你: ")
                    if user_input.lower() in ['quit', 'exit', 'q']:
                        break
                    await client.send_message(user_input)
                except KeyboardInterrupt:
                    break
            print("\n👋 再见!")
        else:
            # 单个消息测试
            await client.send_message(sys.argv[1])
    else:
        # 运行完整测试套件
        await client.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
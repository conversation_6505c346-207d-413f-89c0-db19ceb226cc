import logging
from fastapi import FastAPI
from coz.coz_meals_api_router import coz_meals_api_router

import argparse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI
app = FastAPI()


app.include_router(coz_meals_api_router, prefix="/cal-server/ai/agents", tags=["cal_server"])

if __name__ == "__main__":
    import uvicorn
    parser = argparse.ArgumentParser()
    parser.add_argument('--port', type=int, default=8002)
    args = parser.parse_args()
    uvicorn.run(app, host="127.0.0.1", port=args.port,access_log=False)  # 修改端口为18190
"""

curl -X POST "http://127.0.0.1:8002/cal-server/ai/agents/coz_meals_steam" \
  -H "Content-Type: application/json" \
  -H "timeZoneId: Asia/Shanghai" \
  -H "deviceSn: testDevice001" \
  -H "sessionid: testSession001" \
  -H 'deviceSn: deviceSn' \
  -H 'timeZoneId: Asia/Shanghai' \
  -H 'sessionid: abc1114446344455' \
  -d '{"message": "我想明天8点吃苹果派", "selectedMealNames": ["苹果派"]}'

"""
